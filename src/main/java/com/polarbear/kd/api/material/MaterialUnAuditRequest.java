package com.polarbear.kd.api.material;

import com.polarbear.kd.api.KdResponse;
import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

/**
 * 物料反审核接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【物料】.审核后需修改保存前需 反审核 金蝶云·星空旗舰版.【物料】
 * @date 2025-09-29
 */
public class MaterialUnAuditRequest extends KdOpRequest<MaterialUnAudit> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/basedata/bd_material/unAuditMaterial";
    }

    @Override
    public String logModule() {
        return "kd.bd_material.unAuditMaterial";
    }

    @Override
    public Class<? extends KdResponse<?>> getResponseClass() {
        return MaterialUnAuditResponse.class;
    }

    @Override
    public KdOpRequest<MaterialUnAudit> setLogKey(Config<MaterialUnAudit> config) {
        config.setKey1(materialUnAudit -> {
            if (materialUnAudit != null && materialUnAudit.getNumbers() != null) {
                String keys = String.join(",", materialUnAudit.getNumbers());
                return keys.substring(0, Math.min(keys.length(), 250));
            }
            return "";
        });
        return this;
    }
}
