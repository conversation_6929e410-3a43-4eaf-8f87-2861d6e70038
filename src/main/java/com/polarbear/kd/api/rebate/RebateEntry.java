package com.polarbear.kd.api.rebate;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 返利单明细实体类
 * 
 * <AUTHOR>
 * @date 2025-09-28
 */
@Data
public class RebateEntry {
    
    /**
     * 返利明细.id (修改时需传)
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 返利明细.应返利金额
     */
    @JsonProperty("gjwl_rebateamount")
    private BigDecimal gjwlRebateamount;
    
    /**
     * 返利明细.确认返利金额
     */
    @JsonProperty("gjwl_confirmamount")
    private BigDecimal gjwlConfirmamount;
    
    /**
     * 返利明细.业务日期
     */
    @JsonProperty("gjwl_bizdate")
    private String gjwlBizdate;
    
    /**
     * 返利明细.核心单号
     */
    @JsonProperty("gjwl_mainbillno")
    private String gjwlMainbillno;
    
    /**
     * 返利明细.业务单据
     */
    @JsonProperty("gjwl_bizno")
    private String gjwlBizno;
    
    /**
     * 返利明细.单据类型
     */
    @JsonProperty("gjwl_biztype")
    private String gjwlBiztype;
    
    /**
     * 返利明细.协议编号
     */
    @JsonProperty("gjwl_protocolno")
    private String gjwlProtocolno;
    
    /**
     * 返利明细.协议名称
     */
    @JsonProperty("gjwl_protocolname")
    private String gjwlProtocolname;
    
    /**
     * 返利明细.条款编号
     */
    @JsonProperty("gjwl_clauseno")
    private String gjwlClauseno;
    
    /**
     * 物料编码.编码 (必填)
     */
    @JsonProperty("gjwl_materiel_number")
    private String gjwlMaterielNumber;
    
    /**
     * 返利明细.生产厂家/受托生产企业
     */
    @JsonProperty("gjwl_producer")
    private String gjwlProducer;
    
    /**
     * 返利明细.数量
     */
    @JsonProperty("gjwl_qty")
    private BigDecimal gjwlQty;
    
    /**
     * 返利明细.单价
     */
    @JsonProperty("gjwl_price")
    private BigDecimal gjwlPrice;
    
    /**
     * 返利明细.金额
     */
    @JsonProperty("gjwl_amount")
    private BigDecimal gjwlAmount;
    
    /**
     * 返利明细.批号
     */
    @JsonProperty("gjwl_lotno")
    private String gjwlLotno;
    
    /**
     * 返利明细.生产批号/序列号
     */
    @JsonProperty("gjwl_product_lotno")
    private String gjwlProductLotno;
    
    /**
     * 返利明细.返利点数
     */
    @JsonProperty("gjwl_rebatepoint")
    private BigDecimal gjwlRebatepoint;
    
    /**
     * 返利明细.分摊数量
     */
    @JsonProperty("gjwl_allocateqty")
    private BigDecimal gjwlAllocateqty;
    
    /**
     * 返利明细.协议分类
     */
    @JsonProperty("gjwl_protocoltype")
    private String gjwlProtocoltype;
    
    /**
     * 返利明细.返利类型
     */
    @JsonProperty("gjwl_rebatetype")
    private String gjwlRebatetype;
    
    /**
     * 返利明细.台账ID
     */
    @JsonProperty("gjwl_ledgerid")
    private String gjwlLedgerid;
    
    /**
     * 返利明细.返利来源
     */
    @JsonProperty("gjwl_rebatesource")
    private String gjwlRebatesource;
    
    /**
     * 返利明细.返利支付方式
     */
    @JsonProperty("gjwl_rebatepaytype")
    private String gjwlRebatepaytype;
}
