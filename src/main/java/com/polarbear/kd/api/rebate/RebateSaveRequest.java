package com.polarbear.kd.api.rebate;

import com.polarbear.kd.core.Config;
import com.polarbear.kd.core.KdOpRequest;

import java.util.List;

/**
 * 返利单保存接口
 * 
 * <AUTHOR>
 * @apiNote 广交云.【采购返利单】.审核后 推送 金蝶云·星空旗舰版.【返利单】
 *          广交云.【销售折让单】.审核后 推送 金蝶云·星空旗舰版.【返利单】
 * @date 2025-09-28
 */
public class RebateSaveRequest extends KdOpRequest<List<Rebate>> {

    @Override
    public String getUrlPath() {
        return "/v2/gjwl/gjwl_rebate/gjwl_rebate/saveRebate";
    }

    @Override
    public String logModule() {
        return "kd.gjwl_rebate.saveRebate";
    }

    @Override
    public Class<RebateSaveResponse> getResponseClass() {
        return RebateSaveResponse.class;
    }

    @Override
    public KdOpRequest<List<Rebate>> setLogKey(Config<List<Rebate>> config) {
        config.setKey1(o -> {
            if (o == null || o.isEmpty()) {
                return "";
            }
            String keys = String.join(",", o.stream()
                .map(Rebate::getBillno)
                .filter(billno -> billno != null)
                .toArray(String[]::new));
            return keys.substring(0, Math.min(keys.length(), 250));
        });
        return this;
    }
}
