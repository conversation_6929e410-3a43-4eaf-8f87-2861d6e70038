package com.polarbear.kd.api.material;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.List;

/**
 * 物料反审核接口测试
 * 
 * <AUTHOR>
 * @date 2025-09-29
 */
public class MaterialUnAuditRequestTest {

    private MaterialUnAuditRequest request;
    private MaterialUnAudit materialUnAudit;

    @BeforeEach
    void setUp() {
        request = new MaterialUnAuditRequest();
        materialUnAudit = new MaterialUnAudit();
    }

    @Test
    void testGetUrlPath() {
        assertEquals("/v2/gjwl/basedata/bd_material/unAuditMaterial", request.getUrlPath());
    }

    @Test
    void testLogModule() {
        assertEquals("kd.bd_material.unAuditMaterial", request.logModule());
    }

    @Test
    void testGetResponseClass() {
        assertEquals(MaterialUnAuditResponse.class, request.getResponseClass());
    }

    @Test
    void testMaterialUnAuditParameters() {
        List<String> numbers = Arrays.asList("MAT001", "MAT002", "MAT003");
        materialUnAudit.setNumbers(numbers);
        
        assertNotNull(materialUnAudit.getNumbers());
        assertEquals(3, materialUnAudit.getNumbers().size());
        assertTrue(materialUnAudit.getNumbers().contains("MAT001"));
        assertTrue(materialUnAudit.getNumbers().contains("MAT002"));
        assertTrue(materialUnAudit.getNumbers().contains("MAT003"));
    }

    @Test
    void testRequestDataSetting() {
        List<String> numbers = Arrays.asList("TEST001", "TEST002");
        materialUnAudit.setNumbers(numbers);
        
        request.setData(materialUnAudit);
        
        assertNotNull(request.getData());
        assertEquals(2, request.getData().getNumbers().size());
    }
}
