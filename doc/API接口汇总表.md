
## 接口概览

| 序号 | 接口分类 | 接口名称 | URL路径 | 请求参数类型 | 响应类型 | 描述 |  
|------|----------|----------|---------|--------------|----------|------|  
| 1 | 客户管理 | CustomerSaveRequest | `/v2/gjwl/basedata/bd_customer/saveCustomer` | `List<Customer>` | `CustomerSaveResponse` | 保存客户信息 |  
| 2 | 客户管理 | CustomerSaveExtRequest | `/v2/gjwl/cas/basedata/bd_customer/saveCustomer` | `List<Customer>` | `CustomerSaveExtResponse` | 客户扩展保存 |  
| 3 | 客户管理 | CustomerUnAuditRequest | `/v2/gjwl/basedata/bd_customer/unAuditCustomer` | `CustomerUnAudit` | `CustomerUnAuditResponse` | 客户反审核 |  
| 4 | 供应商管理 | SupplierCreateRequest | `/v2/basedata/bd_supplier/add` | `SupplierCreatePara` | `SimpleResponse` | 创建供应商 |  
| 5 | 供应商管理 | SupplierSaveRequest | `/v2/gjwl/cas/basedata/bd_supplier/saveSupplier` | `SupplierSavePara` | `SupplierSaveResponse` | 保存供应商 |  
| 6 | 供应商管理 | SupplierUpdateRequest | `/v2/basedata/bd_supplier/batchUpdate` | `List<SupplierUpdatePara>` | `SimpleResponse` | 批量更新供应商 |  
| 7 | 供应商管理 | SupplierUnAuditRequest | `/v2/gjwl/basedata/bd_supplier/unAuditSupplier` | `UnAuditPara` | `SupplierUnAuditResponse` | 供应商反审核 |  
| 8 | 收款管理 | PaymentRequest | `/v2/gjwl/cas/cas_recbill/saveRecBill` | `List<PaymentPara>` | `PaymentResponse` | 收款单新增保存 |  
| 9 | 收款管理 | ReceiptRecordQueryRequest | `/v2/gjwl/cas/transDetail/transDetailNewQuery` | `ReceiptRecordQueryParams` | `DetailQueryResponse` | 收款流水查询 |  
| 10 | 收款管理 | ReceiptClaimParamsRequest | `/v2/gjwl/cas/recClaimInterface/recClaimInterface` | `ReceiptClaimParams` | `ReceiptClaimResponse` | 收款流水认领 |  
| 11 | 收款管理 | CancelClaimRequest | `/v2/gjwl/cas/cancelRecClaim/calcelrecclaiminterface` | `CancelClaimParams` | `ReceiptClaimResponse` | 取消认领 |  
| 12 | 付款管理 | PayApplyQueryRequest | `/v2/gjwl/ap/payApply/payApplyQuery` | `PayApplyQueryParam` | `PayApplyQueryResponse` | 付款申请查询 |  
| 13 | 付款管理 | PayApplyAutoCreateRequest | `/v2/gjwl/ap/payApply/payApplyAutoCreate` | `List<Claim>` | `PayApplyAutoCreateResponse` | 付款申请自动创建 |  
| 14 | 付款管理 | AddKdPaymentRequest | `/v2/gjwl/cas/cas_paybill/savePayBill` | `List<AddKdPaymentPara>` | `AddKdPaymentResponse` | 金蝶付款单新增 |  
| 15 | 付款管理 | ClaimQueryRequest | `/v2/gjwl/cas/billSatus/billstatusquery` | `ClaimQueryPara` | `PayLineResultResponse` | 查询报账单状态 |
| 16 | 项目管理 | ProjectSaveRequest | `/v2/gjwl/basedata/bd_project/saveProject` | `List<Project>` | `ProjectSaveResponse` | 项目保存提交审核 |
| 17 | 项目管理 | ProjectUnAuditRequest | `/v2/basedata/bd_project/batchUnaudit` | `ProjectUnAudit` | `ProjectUnAuditResponse` | 项目反审核 |
| 18 | 物料管理 | MaterialSaveRequest | `/v2/gjwl/gjwl_basedata_ext/saveMaterial` | `MaterialSaveRequest.MaterialSaveData` | `MaterialSaveResponse` | 物料批量保存提交审核 |
| 19 | 物料管理 | MaterialInventoryUpdateRequest | `/v2/sbd/bd_materialinventoryinfo/batchUpdate` | `List<MaterialInventoryUpdatePara>` | `MaterialInventoryUpdateResponse` | 物料库存信息批量更新 |
| 20 | 物料管理 | MaterialUnAuditRequest | `/v2/gjwl/basedata/bd_material/unAuditMaterial` | `MaterialUnAudit` | `MaterialUnAuditResponse` | 物料反审核 |
| 21 | 采购订单管理 | PurchaseOrderSaveRequest | `/v2/gjwl/pm/pm_purorderbill/savePurOrder` | `List<PurchaseOrder>` | `PurchaseOrderSaveResponse` | 采购订单保存 |
| 22 | 销售订单管理 | SalesOrderSaveRequest | `/v2/gjwl/sm/sm_salorder/saveSalOrder` | `List<SalesOrder>` | `SalesOrderSaveResponse` | 销售订单保存 |
| 23 | 销售出库管理 | SalesOutBillSaveRequest | `/v2/gjwl/im/im_saloutbill/saveSaloutBill` | `List<SalesOutBill>` | `SalesOutBillSaveResponse` | 销售出库退货单保存 |
| 24 | 销售调价管理 | SalPriceAdjustSaveRequest | `/v2/gjwl/im/gjwl_salpriceadjust/saveSalPriceAdjust` | `List<SalPriceAdjust>` | `SalPriceAdjustSaveResponse` | 销售调价单保存 |
| 25 | 库存管理 | SurplusBillSaveRequest | `/v2/gjwl/im/im_surplusbill/saveSurpluseBill` | `List<SurplusBill>` | `SurplusBillSaveResponse` | 盘盈单保存 |
| 26 | 库存管理 | DeficitBillSaveRequest | `/v2/gjwl/im/im_deficitbill/saveDeficitBill` | `List<DeficitBill>` | `DeficitBillSaveResponse` | 盘亏单保存 |
| 27 | 库存管理 | TransOutBillSaveRequest | `/v2/gjwl/im/im_transoutbill/SaveTransOutBill` | `List<TransOutBill>` | `TransOutBillSaveResponse` | 分步调出单保存 |
| 28 | 库存管理 | TransInBillSaveRequest | `/v2/im/im_transinbill/batchAdd_V2` | `List<TransInBill>` | `TransInBillSaveResponse` | 分步调入单保存 |
| 29 | 库存管理 | OtherOutBillSaveRequest | `/v2/gjwl/im/im_otheroutbill/saveOtherOutBill` | `List<OtherOutBill>` | `OtherOutBillSaveResponse` | 其他出库单保存 |
| 30 | 采购调价管理 | PurPriceAdjustSaveRequest | `/v2/gjwl/im/gjwl_purpriceadjust/savePurPriceAdjust` | `List<PurPriceAdjust>` | `PurPriceAdjustSaveResponse` | 采购调价单保存 |
| 31 | 采购调税管理 | PurTaxAdjustSaveRequest | `/v2/gjwl/im/gjwl_purtaxadjust/savePurTaxAdjust` | `List<PurTaxAdjust>` | `PurTaxAdjustSaveResponse` | 采购调税单保存 |
| 32 | 采购入库管理 | PurInBillSaveRequest | `/v2/gjwl/im/im_purinbill/savePruinBill` | `List<PurInBill>` | `PurInBillSaveResponse` | 采购入库退料单保存 |
| 33 | 下推并保存 | PushAndSaveRequest | `/v2/gjwl/msbd/pushAndSave` | `PushAndSaveRequestParam` | `PushAndSaveResponse` | 下推并保存接口 |
| 34 | 信用管理 | CreditStatusQueryRequest | `/v2/ccm/credit/getCreditBalance` | `CreditStatusQueryParam` | `CreditStatusQueryResponse` | 查询信用状况接口 |
| 35 | 返利单管理 | RebateSaveRequest | `/v2/gjwl/gjwl_rebate/gjwl_rebate/saveRebate` | `List<Rebate>` | `RebateSaveResponse` | 返利单保存接口 |
| 36 | 附件管理 | AttachmentUploadRequest | `/v2/frame/attachment/uploadFile` | `AttachmentUploadRequestParam` | `AttachmentUploadResponse` | 附件上传接口 |

## 详细参数说明

### 客户管理接口参数

#### Customer (客户信息)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| number | String | 是 | 客户编码 |  
| name | String | 是 | 客户名称 |  
| ... | ... | ... | 其他客户属性 |  

#### CustomerUnAudit (客户反审核参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| customerId | String | 是 | 客户ID |  
| reason | String | 否 | 反审核原因 |  

### 供应商管理接口参数

#### SupplierCreatePara (供应商创建参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| number | String | 是 | 供应商编码 |  
| name | String | 是 | 供应商名称 |  
| entryLinkman | List<Linkman> | 否 | 联系人分录 |  
| entryBank | List<BankInfo> | 否 | 银行信息分录 |  
| entryGroupStandard | List<GroupStandard> | 否 | 分类标准 |  
| entryTax | List<TaxQualification> | 否 | 税务资质 |  

#### SupplierSavePara (供应商保存参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| number | String | 是 | 供应商编码 |  
| gjwlAppid | String | 是 | 应用ID |  
| name | String | 是 | 供应商名称 |  

### 收款管理接口参数

#### PaymentPara (收款单参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| billNo | String | 是 | 单据编号 |  
| bizDate | LocalDate | 是 | 业务日期 |  
| payerType | String | 是 | 付款单位类型 |  
| txtDescription | String | 否 | 摘要 |  
| actRecAmt | BigDecimal | 是 | 收款金额 |  
| exchangeRate | BigDecimal | 是 | 汇率 |  
| localAmt | BigDecimal | 是 | 折本位币 |  
| payerName | String | 否 | 付款单位名称 |  
| payerAcctBankNum | String | 否 | 付款账户 |  
| settleTNumber | String | 否 | 结算号 |  
| payer | Long | 否 | 付款单位ID |  
| payerFormId | String | 否 | 付款单位类型标识ID |  
| payerAccFormId | String | 否 | 付款账户类型标识ID |  
| payerAcctBank | Long | 否 | 付款账户ID |  
| billTypeId | Long | 否 | 单据类型ID |  
| billTypeNumber | String | 否 | 单据类型编码 |  
| payerBankName | String | 否 | 付款银行 |  
| isAgent | Boolean | 否 | 代理收款 |  
| paymentMode | String | 否 | 付款方式 |  
| isRefund | Boolean | 否 | 退款退票业务 |  
| isFullRefund | Boolean | 否 | 全额退款 |  
| exRateDate | LocalDate | 否 | 汇率日期 |  
| entries | List<Entry> | 否 | 收款明细 |  
| entryEntities | List<EntryEntity> | 否 | 单据体列表 |  
| infoEntries | List<InfoEntry> | 否 | 流水信息列表 |  
| draftBills | List<DraftBill> | 否 | 结算号基础资料列表 |  
| orgId | Long | 否 | 收款组织ID |  
| orgNumber | String | 否 | 收款组织编码 |  
| payeeBankId | Long | 否 | 收款银行ID |  
| payeeBankNumber | String | 否 | 收款银行编码 |  

#### ReceiptRecordQueryParams (收款流水查询参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| compCode | String | 是 | 公司编码 |  

#### ReceiptClaimParams (收款流水认领参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| compCode | String | 是 | 公司编码 |  
| claimRevInforBean | ReceiptClaimItem | 是 | 认领信息 |  

#### ReceiptClaimItem (认领信息)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| receivablesId | Long | 是 | 收款流水ID |  
| receivablesNum | String | 是 | 收款流水编号 |  

#### CancelClaimParams (取消认领参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| compCode | String | 是 | 公司编码 |  
| operateType | String | 是 | 操作类型 (UNCLAIM取消认领, AUDIT认领复核) |  
| receivablesId | Long | 是 | 收款流水ID |  
| remark | String | 否 | 操作原因 |  
| casRecBillId | String | 是 | 金蝶收款单据ID |  

### 付款管理接口参数

#### PayApplyQueryParam (付款申请查询参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| ... | ... | ... | 查询条件字段 |  

#### AddKdPaymentPara (金蝶付款单参数)
| 字段名 | 类型 | 必填 | 描述 |  
|--------|------|------|------|  
| billno | String | 是 | 单据编号 |  
| bizdate | Date | 是 | 业务日期 |  
| entry | List<AddKdPaymentDetail> | 是 | 分录 |  
| payeetype | String | 是 | 收款单位类型 |  
| description | String | 否 | 描述 |  

#### ClaimQueryPara (报账单状态查询参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| claimNos | String | 是 | 报账单号(多个用逗号分隔) |

### 项目管理接口参数

#### Project (项目信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 项目ID (修改时需传) |
| number | String | 是 | 项目编码 |
| name | String | 是 | 项目名称 |
| createorg_number | String | 是 | 创建组织编码 |
| gjwl_appid | String | 是 | 外部编码 |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |

#### ProjectUnAudit (项目反审核参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | List<String> | 是 | 项目ID列表 |

### 物料管理接口参数

#### MaterialSaveRequest.MaterialSaveData (物料保存请求数据)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materials | List<MaterialDataVo> | 否 | 物料数据列表 |

#### MaterialDataVo (物料数据)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materialInfoVo | MaterialInfoVo | 否 | 物料主档信息 |
| commonInfoVo | CommonInfoVo | 否 | 物料组织公共信息 |

#### MaterialInfoVo (物料主档信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 物料ID |
| name | String | 是 | 物料名称 |
| number | String | 否 | 物料编码 |
| gjwl_appid | String | 是 | 外部编码 |
| createorg | String | 是 | 创建组织编码(固定值"gjwl") |
| baseunit | String | 是 | 基本单位编码 |
| modelnum | String | 否 | 规格型号 |
| unitconvertdir | String | 否 | 换算方向(固定值"A") |
| entry_groupstandard | List<GroupStandardEntry> | 否 | 分类信息分录 |

#### CommonInfoVo (物料组织公共信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| materialtype | String | 是 | 物料类型(固定值"1") |
| materialattr | String | 是 | 物料属性(固定值"10040") |
| enablepur | Boolean | 否 | 采购信息(固定值true) |
| enablesale | Boolean | 否 | 销售信息(固定值true) |
| enableinv | Boolean | 否 | 库存信息(固定值true) |
| group | String | 否 | 存货类别(固定值"CHJZFL06-SYS") |

#### MaterialInventoryUpdatePara (物料库存更新参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| enableshelflifemgr | Boolean | 是 | 保质期管理 |
| shelflifeunit | String | 是 | 保质期单位(day/month/year) |
| shelflife | Integer | 否 | 保质期 |
| caldirection | String | 否 | 计算方向(固定值"4") |
| startdatecaltype | String | 否 | 生产日计算方式(固定值"1") |
| calculationforenddate | String | 是 | 到期日计算方式(固定值"0") |
| leadtimeunit | String | 是 | 提前期单位(day/month/year) |
| masterid_number | String | 是 | 物料编码 |
| createorg_number | String | 是 | 库存信息创建组织编码(固定值"gjwl") |

### 采购订单管理接口参数

#### PurchaseOrder (采购订单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 订单ID (修改时需传) |
| billno | String | 否 | 单据编号 |
| gjwl_thirdparty_billno | String | 否 | 外部系统单号 |
| gjwl_sourcesystemtype | String | 否 | 来源系统 (固定值"广交云供应链管理系统") |
| org_number | String | 是 | 采购组织编码 (固定值"1011") |
| billtype_number | String | 是 | 单据类型编码 (固定值"pm_PurOrderBill_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"110") |
| dept_number | String | 否 | 采购部门编码 (固定值"GJEY1102") |
| biztime | String | 是 | 订单日期 |
| supplier_number | String | 是 | 订货供应商编码 |
| paycondition_number | String | 是 | 付款条件编号 |
| settlecurrency_number | String | 是 | 结算币别货币代码 (固定值"CNY") |
| comment | String | 否 | 备注 |
| istax | Boolean | 否 | 含税 |
| paymode | String | 是 | 付款方式 (固定值"CREDIT") |
| exratedate | String | 是 | 汇率日期 (同业务日期) |
| exratetable_number | String | 是 | 汇率表编码 (固定值"ERT-01") |
| exchangerate | BigDecimal | 是 | 汇率 (固定值1) |
| ispayrate | Boolean | 否 | 按比例(%) |
| trdbillno | String | 是 | 第三方业务编码 (传外部系统单号) |
| billentry | List<PurchaseOrderEntry> | 是 | 物料明细 |
| purbillentry_pay | List<PurchaseOrderPayEntry> | 否 | 付款计划 |

#### PurchaseOrderEntry (采购订单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 明细ID (修改时必传) |
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 |
| unit_number | String | 是 | 采购单位编码 |
| qty | BigDecimal | 是 | 数量 |
| price | BigDecimal | 否 | 单价 |
| priceandtax | BigDecimal | 否 | 含税单价 |
| warehouse_number | String | 否 | 仓库编码 |
| discounttype | String | 是 | 折扣方式 (A:折扣率%, B:单位折扣额, NULL:无) |
| discountrate | BigDecimal | 否 | 单位折扣率 |
| discountamount | BigDecimal | 否 | 折扣额 |
| taxrateid_number | String | 否 | 税率编码 |
| entryreqorg_number | String | 是 | 需求组织编码 (固定值"1011") |
| entryrecorg_number | String | 是 | 收料组织编码 (固定值"1011") |
| entrysettleorg_number | String | 是 | 结算组织编码 (固定值"1011") |
| ownertype | String | 是 | 货主类型 (固定值"bos_org") |
| owner_number | String | 是 | 货主编码 (固定值"1011") |
| promisedate | String | 否 | 承诺日期 |
| deliverdate | String | 是 | 交货日期 |
| ispresent | Boolean | 否 | 赠品 |
| project_number | String | 否 | 项目编码 |
| entrycomment | String | 否 | 明细备注 |

#### PurchaseOrderPayEntry (采购订单付款计划信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 付款计划ID |
| planentrysettleorg_number | String | 否 | 结算组织编码 (固定值"1011") |
| paydate | String | 否 | 到期日 |
| payrate | BigDecimal | 否 | 应付比例(%) |
| payamount | BigDecimal | 否 | 应付金额 |
| isprepay | Boolean | 否 | 是否预付 |

### 销售订单管理接口参数

#### SalesOrder (销售订单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 订单ID (修改时需传) |
| bizdate | String | 是 | 订单日期 |
| org_number | String | 是 | 销售组织编码 (固定值"1011") |
| customer_number | String | 是 | 订货客户编码 |
| billtype_number | String | 是 | 单据类型编码 (固定值"sm_SalesOrder_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"210") |
| settlecurrency_number | String | 否 | 结算币别货币代码 (固定值"CNY") |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (广交云订单编号) |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| comment | String | 否 | 表头备注 |
| istax | Boolean | 否 | 含税标识 |
| iswholediscount | Boolean | 否 | 录入整单折扣标识 |
| wholediscountamount | BigDecimal | 否 | 整单折扣额 |
| reccondition_number | String | 是 | 收款条件编号 |
| billentry | List<SalesOrderEntry> | 是 | 物料明细列表 |

#### SalesOrderEntry (销售订单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 明细ID (修改时需传) |
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 (广交云产品编码) |
| unit_number | String | 是 | 销售单位编码 |
| e_stockorg_number | String | 是 | 发货组织编码 (固定值"1011") |
| entrysettleorg_number | String | 是 | 结算组织编码 (固定值"1011") |
| price | BigDecimal | 否 | 单价 |
| priceandtax | BigDecimal | 否 | 含税单价 |
| qty | BigDecimal | 是 | 数量 |
| taxrateid_number | String | 否 | 税率编码 |
| taxamount | BigDecimal | 否 | 税额 |
| discounttype | String | 否 | 折扣方式 (A:折扣率%, B:单位折扣额, NULL:无) |
| discountrate | BigDecimal | 否 | 单位折扣率 |
| discountamount | BigDecimal | 否 | 折扣额 |
| remark | String | 否 | 备注 |
| warehouse_number | String | 否 | 仓库编码 |
| amountandtax | BigDecimal | 否 | 价税合计 |
| amount | BigDecimal | 否 | 金额 |
| ispresent | Boolean | 否 | 赠品标识 (默认false，金额为0传true) |
| project_number | String | 否 | 项目编码 |
| lotnumber | String | 否 | 批号 (对应广交云批次) |
| gjwl_product_lotno | String | 否 | 生产批号 (对应广交云生产批号) |

### 销售调价单管理接口参数

#### SalPriceAdjust (销售调价单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 单据ID (修改时需传) |
| billno | String | 是 | 单据编号 |
| org_number | String | 是 | 组织编码 (固定值"1011") |
| gjwl_date | String | 是 | 业务日期 |
| gjwl_customer_number | String | 是 | 客户编码 |
| gjwl_reason | String | 是 | 调价原因 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| entryentity | List<SalPriceAdjustEntry> | 否 | 物料明细列表 |

#### SalPriceAdjustEntry (销售调价单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 明细ID (修改时需传) |
| gjwl_salorderbillno | String | 否 | 销售单号 |
| gjwl_billno | String | 否 | 单据编号 |
| gjwl_billtype | String | 否 | 单据类型 |
| gjwl_materiel_number | String | 否 | 物料编码 |
| gjwl_product_lotno | String | 否 | 生产批号/序列号 |
| gjwl_taxrate_number | String | 是 | 税率编码 |
| gjwl_qty | BigDecimal | 否 | 数量 |
| gjwl_oldtaxprice | BigDecimal | 否 | 原单价（含税） |
| gjwl_newesttaxprice | BigDecimal | 否 | 调整前最新价（含税） |
| gjwl_taxprice | BigDecimal | 否 | 本次调整单价（含税） |
| gjwl_adjustamount | BigDecimal | 否 | 调整金额 |

### 采购调价单管理接口参数

#### PurPriceAdjust (采购调价单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 单据ID (修改时需传) |
| billno | String | 是 | 单据编号 |
| org_number | String | 是 | 组织编码 (固定值"1011") |
| gjwl_date | String | 是 | 业务日期 |
| gjwl_type | String | 是 | 调价类型 (0：入库调价，1：返利调价) |
| gjwl_instockbillno | String | 是 | 采购入库单号 |
| gjwl_reason | String | 是 | 调价原因 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| gjwl_supplier_number | String | 是 | 供应商编码 |
| entryentity | List<PurPriceAdjustEntry> | 否 | 物料明细列表 |

#### PurPriceAdjustEntry (采购调价单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 明细ID (修改时需传) |
| gjwl_materiel_masterid_number | String | 是 | 物料编码 |
| gjwl_producer | String | 否 | 生产厂家/受托生产企业 |
| gjwl_qty | BigDecimal | 否 | 数量 |
| gjwl_lotno | String | 否 | 批号 (对应广交云的批次) |
| gjwl_product_lotno | String | 否 | 生产批号/序列号 |
| gjwl_price | BigDecimal | 否 | 采购价 |
| gjwl_untaxamount | BigDecimal | 否 | 不含税金额 |
| gjwl_tax | BigDecimal | 否 | 税额 |
| gjwl_invoiceno | String | 否 | 发票号码 |
| gjwl_returnqty | BigDecimal | 否 | 当前退货数量 |
| gjwl_afterprice | BigDecimal | 否 | 调整后采购价 |
| gjwl_instockdiffamount | BigDecimal | 否 | 入库调整差额 |
| gjwl_returndiffamount | BigDecimal | 否 | 采退调差金额 |
| gjwl_realdiffamount | BigDecimal | 否 | 实际调整差额 |

### 采购调税单管理接口参数

#### PurTaxAdjust (采购调税单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 单据ID (修改时需传) |
| billno | String | 否 | 单据编号 |
| org_number | String | 是 | 组织编码 (固定值"1011") |
| gjwl_date | String | 是 | 业务日期 |
| gjwl_supplier_number | String | 是 | 供应商编码 |
| gjwl_reason | String | 是 | 税率变更原因 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| entryentity | List<PurTaxAdjustEntry> | 否 | 物料明细列表 |
| gjwl_hisentity | List<PurTaxAdjustHisEntry> | 否 | 税率变更金额调整单列表 |

#### PurTaxAdjustEntry (采购调税单物料明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 明细ID (修改时需传) |
| gjwl_purorderbillno | String | 否 | 采购单号 |
| gjwl_instockbillno | String | 否 | 入库单号 |
| gjwl_materiel_number | String | 否 | 物料编码 |
| gjwl_producer | String | 否 | 生产厂家/受托生产企业 |
| gjwl_product_lotno | String | 否 | 生产批号/序列号 |
| gjwl_warehouse_number | String | 是 | 仓库编码 |
| gjwl_qty | BigDecimal | 否 | 数量 |
| gjwl_taxprice | BigDecimal | 否 | 采购价（含税） |
| gjwl_oldtaxrate_number | String | 是 | 原单税率编码 |
| gjwl_newesttaxrate_number | String | 是 | 调整前最新税率编码 |
| gjwl_taxrate_number | String | 是 | 本次调整税率编码 |

#### PurTaxAdjustHisEntry (税率变更金额调整单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 明细ID (修改时需传) |
| gjwl_purorderbillno_h | String | 是 | 采购单号 |
| gjwl_billno_h | String | 是 | 单据编号 |
| gjwl_materiel_h_masterid_number | String | 是 | 物料编码 |
| gjwl_product_lotno_h | String | 否 | 生产批号/序列号 |
| gjwl_taxrate_h_number | String | 是 | 税率编码 |
| gjwl_qty_h | BigDecimal | 否 | 数量 |
| gjwl_taxprice_h | BigDecimal | 否 | 单价（含税） |
| gjwl_adjusttype | String | 否 | 调整类型 |
| gjwl_allamount | BigDecimal | 否 | 金额（含税） |
| gjwl_amount | BigDecimal | 否 | 金额（不含税） |
| gjwl_tax | BigDecimal | 否 | 税额 |

### 销售出库管理接口参数

#### SalesOutBill (销售出库退货单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 单据ID (修改时需传) |
| org_number | String | 是 | 库存组织编码 (固定值"1011") |
| billno | String | 是 | 单据编号 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (传广交云单号) |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| billtype_number | String | 是 | 单据类型编码 (销售出库单-im_SalOutBill_STD_BT_S, 销售退货单-im_SalOutBill_STD_BT_S_R) |
| biztype_number | String | 是 | 业务类型编码 (210-物料类销售, 2101-物料类销售退货) |
| invscheme_number | String | 是 | 库存事务编码 (210-物料类销售, 2101-普通销售退、补货) |
| biztime | String | 是 | 业务日期 |
| exratedate | String | 是 | 汇率日期 (同业务日期) |
| customer_number | String | 是 | 客户编码 |
| paymode | String | 是 | 付款方式 (CREDIT-赊销、CASH-现销) |
| bizorg_number | String | 是 | 销售组织编码 (固定值"1011") |
| currency_number | String | 是 | 本位币货币代码 (固定值"CNY") |
| settlecurrency_number | String | 是 | 结算币别货币代码 (固定值"CNY") |
| exratetable_number | String | 是 | 汇率表编码 (固定值"ERT-01") |
| exchangerate | BigDecimal | 是 | 汇率 (固定值1) |
| istax | Boolean | 是 | 含税标识 |
| reccondition_number | String | 是 | 收款条件编号 |
| iswholediscount | Boolean | 是 | 录入整单折扣标识 |
| wholediscountamount | String | 否 | 整单折扣额 |
| comment | String | 否 | 备注 |
| billentry | List<SalesOutBillEntry> | 是 | 物料明细列表 |

#### SalesOutBillEntry (销售出库退货单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 明细ID (修改时需传) |
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 (产品编码) |
| unit_number | String | 是 | 库存单位编码 |
| baseunit_number | String | 是 | 基本单位编码 (同库存单位编码) |
| qty | BigDecimal | 是 | 数量 |
| baseqty | BigDecimal | 是 | 基本数量 (同数量) |
| warehouse_number | String | 是 | 仓库编码 |
| outinvtype_number | String | 是 | 出库库存类型编码 (固定值"110") |
| outinvstatus_number | String | 是 | 出库库存状态编码 (固定值"110") |
| outownertype | String | 是 | 出库货主类型 (固定值"bos_org") |
| outowner_number | String | 是 | 出库货主编码 (固定值"1011") |
| outkeepertype | String | 是 | 出库保管者类型 (固定值"bos_org") |
| outkeeper_number | String | 是 | 出库保管者编码 (固定值"1011") |
| entrysettleorg_number | String | 是 | 结算组织编码 (固定值"1011") |
| price | BigDecimal | 是 | 单价 |
| priceandtax | BigDecimal | 是 | 含税单价 |
| taxrateid_number | String | 是 | 税率编码 |
| taxamount | BigDecimal | 是 | 税额 |
| amountandtax | BigDecimal | 是 | 价税合计 |
| amount | BigDecimal | 是 | 金额 |
| ispresent | Boolean | 是 | 赠品标识 (默认false，金额为0传true) |
| discounttype | String | 否 | 折扣方式 (A:折扣率%, NULL:无, B:单位折扣额) |
| discountrate | BigDecimal | 否 | 单位折扣率 |
| discountamount | BigDecimal | 否 | 折扣额 |
| entrycomment | String | 否 | 明细备注 |
| project_number | String | 否 | 项目编码 |
| producedate | String | 否 | 生产日期 (物料启用保质期时必传) |
| expirydate | String | 否 | 到期日期 (物料启用保质期时必传) |
| lotnumber | String | 否 | 批号 (物料启用批号时必传) |
| gjwl_product_lotno | String | 否 | 生产批号 |

### 库存管理接口参数

#### SurplusBill (盘盈单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| org_number | String | 是 | 库存组织编码 (固定值"1011") |
| billno | String | 否 | 单据编号 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (传广交云单号) |
| bookdate | String | 是 | 记账日期 |
| billtype_number | String | 是 | 单据类型编码 (固定值"im_InvCheckIn_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"350") |
| biztime | String | 是 | 业务日期 |
| invscheme_number | String | 是 | 库存事务编码 (固定值"350") |
| comment | String | 否 | 备注 |
| billentry | List<SurplusBillEntry> | 是 | 物料明细 |

#### SurplusBillEntry (盘盈单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 (广交产品编码) |
| invstatus_number | String | 是 | 入库库存状态编码 (固定值"110") |
| invtype_number | String | 是 | 入库库存类型编码 (固定值"110") |
| owner_number | String | 是 | 货主编码 (固定值"1011") |
| keeper_number | String | 是 | 保管者编码 (固定值"1011") |
| ownertype | String | 是 | 货主类型 (固定值"bos_org") |
| keepertype | String | 是 | 保管者类型 (固定值"bos_org") |
| unit_number | String | 是 | 库存单位编码 |
| warehouse_number | String | 是 | 仓库编码 |
| qty | BigDecimal | 否 | 盘点数量（库存） |
| invgainqty | BigDecimal | 是 | 盘盈数量（库存） (传差异数量) |
| invqtyacc | BigDecimal | 否 | 账存数量（库存） |
| baseunit_number | String | 是 | 基本单位编码 (同库存单位编码) |
| baseqty | BigDecimal | 是 | 盘点数量（基本） (同盘点数量库存) |
| lotnumber | String | 否 | 批号 (对应广交云批次) |
| gjwl_product_lotno | String | 否 | 生产批号 (对应广交云生产批号) |
| project_number | String | 否 | 项目编码 |
| producedate | String | 否 | 生产日期 |
| expirydate | String | 否 | 有效期至 |
| entrycomment | String | 否 | 单据体备注 |

#### DeficitBill (盘亏单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| org_number | String | 是 | 库存组织编码 |
| billno | String | 否 | 单据编号 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (传广交云单号) |
| bookdate | String | 是 | 记账日期 |
| billtype_number | String | 是 | 单据类型编码 (固定值"im_InvCheckOut_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"351") |
| biztime | String | 是 | 业务日期 |
| invscheme_number | String | 是 | 库存事务编码 (固定值"351") |
| dept_number | String | 否 | 部门编码 |
| billentry | List<DeficitBillEntry> | 是 | 物料明细 |

#### DeficitBillEntry (盘亏单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 |
| outinvstatus_number | String | 是 | 出库库存状态编码 (固定值"110") |
| outinvtype_number | String | 是 | 出库库存类型编码 (固定值"110") |
| outowner_number | String | 是 | 货主编码 |
| outkeeper_number | String | 是 | 保管者编码 |
| outownertype | String | 是 | 货主类型 (固定值"bos_org") |
| outkeepertype | String | 是 | 保管者类型 (固定值"bos_org") |
| unit_number | String | 是 | 库存单位编码 |
| warehouse_number | String | 是 | 仓库编码 |
| qty | BigDecimal | 否 | 盘点数量（库存） |
| invqtyacc | BigDecimal | 否 | 账存数量（库存） |
| invlossqty | BigDecimal | 是 | 盘亏数量（库存） (传差异数量) |
| baseunit_number | String | 是 | 基本单位编码 |
| baseqty | BigDecimal | 是 | 盘点数量（基本） |
| lotnumber | String | 否 | 批号 (对应广交云批次) |
| gjwl_product_lotno | String | 否 | 生产批号 (对应广交云生产批号) |

#### TransOutBill (分步调出单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| billno | String | 否 | 单据编号 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (传广交云单号) |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| biztime | String | 是 | 业务日期 |
| comment | String | 否 | 备注 |
| transtype | String | 是 | 调拨类型 (固定值"A") |
| org_number | String | 是 | 调出组织编码 (固定值"1011") |
| billtype_number | String | 是 | 单据类型编码 (固定值"im_AllotOutBill_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"310") |
| invscheme_number | String | 是 | 库存事务编码 (固定值"315") |
| inorg_number | String | 是 | 调入组织编码 (固定值"1011") |
| settlescurrency_number | String | 否 | 本位币货币代码 (固定值"CNY") |
| billentry | List<TransOutBillEntry> | 是 | 物料明细 |

#### TransOutBillEntry (分步调出单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 物料明细ID |
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 (广交产品编码) |
| unit_number | String | 否 | 库存单位编码 |
| qty | BigDecimal | 是 | 物料明细数量 |
| warehouse_number | String | 是 | 调出仓库编码 |
| lotnumber | String | 否 | 批号 (物料启用批号时必传) |
| gjwl_product_lotno | String | 否 | 生产批号 (对应广交云生产批号) |
| owner_number | String | 否 | 调入货主编码 (固定值"1011") |
| producedate | String | 否 | 生产日期 (物料启用保质期时必传) |
| expirydate | String | 否 | 到期日期 (物料启用保质期时必传) |
| outinvstatus_number | String | 是 | 调出库存状态编码 (固定值"110") |
| outinvtype_number | String | 是 | 调出库存类型编码 (固定值"110") |
| outownertype | String | 是 | 调出货主类型 (固定值"bos_org") |
| outowner_number | String | 是 | 调出货主编码 (固定值"1011") |
| outkeepertype | String | 是 | 调出保管者类型 (固定值"bos_org") |
| outkeeper_number | String | 是 | 调出保管者编码 (固定值"1011") |
| isfreegift | Boolean | 否 | 赠品标识 |
| entrycomment | String | 否 | 物料明细备注 |
| inwarehouse_number | String | 否 | 调入仓库编码 |
| ownertype | String | 是 | 调入货主类型 (固定值"bos_org") |
| keepertype | String | 是 | 调入保管者类型 (固定值"bos_org") |
| keeper_number | String | 是 | 调入保管者编码 (固定值"1011") |
| invstatus_number | String | 是 | 调入库存状态编码 (固定值"110") |
| invtype_number | String | 是 | 调入库存类型编码 (固定值"110") |
| project_number | String | 否 | 调出项目编码 |
| inproject_number | String | 否 | 调入项目编码 |

#### TransInBill (分步调入单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| org_number | String | 是 | 调入组织编码 (固定值"BU-001") |
| billno | String | 否 | 单据编号 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (传广交云单号) |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| biztime | String | 是 | 业务日期 |
| billtype_number | String | 是 | 单据类型编码 (固定值"im_AllotInBill_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"310") |
| invscheme_number | String | 是 | 库存事务编码 (固定值"316") |
| transtype | String | 是 | 调拨类型 (固定值"A") |
| transit | String | 是 | 在途归属 (固定值"A") |
| outorg_number | String | 是 | 调出组织编码 (固定值"BU-002") |
| settlescurrency_number | String | 是 | 本位币货币代码 (固定值"CNY") |
| comment | String | 否 | 备注 |
| billentry | List<TransInBillEntry> | 是 | 物料明细 |

#### TransInBillEntry (分步调入单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 物料明细ID |
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 |
| mversion_number | String | 否 | 物料版本编码 |
| unit_number | String | 是 | 库存单位编码 |
| qty | BigDecimal | 是 | 物料明细数量 |
| warehouse_number | String | 是 | 调入仓库编码 |
| invstatus_number | String | 是 | 调入库存状态编码 (固定值"110") |
| ownertype | String | 是 | 调入货主类型 (固定值"bos_org") |
| owner_number | String | 是 | 调入货主编码 (固定值"BU-001") |
| keepertype | String | 是 | 调入保管者类型 (固定值"bos_org") |
| keeper_number | String | 是 | 调入保管者编码 (固定值"BU-001") |
| producedate | String | 否 | 生产日期 (物料启用保质期时必传) |
| expirydate | String | 否 | 有效期至 (物料启用保质期时必传) |
| invtype_number | String | 是 | 调入库存类型编码 (固定值"110") |
| outinvstatus_number | String | 是 | 调出库存状态编码 (固定值"114") |
| outinvtype_number | String | 是 | 调出库存类型编码 (固定值"110") |
| outownertype | String | 是 | 调出货主类型 (固定值"bos_org") |
| outkeepertype | String | 是 | 调出保管者类型 (固定值"bos_org") |
| outowner_number | String | 是 | 调出货主编码 |
| outkeeper_number | String | 是 | 调出保管者编码 |
| outwarehouse_number | String | 是 | 调出仓库编码 |

#### OtherOutBill (其他出库单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| org_number | String | 是 | 库存组织编码 (固定值"1011") |
| billno | String | 否 | 单据编号 |
| gjwl_thirdparty_billno | String | 是 | 外部系统单号 (传广交云单号) |
| gjwl_sourcesystemtype | String | 是 | 来源系统 (固定值"广交云供应链管理系统") |
| biztime | String | 是 | 业务日期 |
| bookdate | String | 否 | 记账日期 |
| billtype_number | String | 是 | 单据类型编码 (固定值"im_OtherOutBill_STD_BT_S") |
| biztype_number | String | 是 | 业务类型编码 (固定值"355") |
| invscheme_number | String | 是 | 库存事务编码 (固定值"355") |
| bizdept_number | String | 是 | 领用部门编码 (固定值"1011EY11EY1102") |
| comment | String | 否 | 备注 |
| billentry | List<OtherOutBillEntry> | 是 | 物料明细 |

#### OtherOutBillEntry (其他出库单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| linetype_number | String | 是 | 行类型编码 (固定值"010") |
| material_number | String | 是 | 物料编码 (广交产品编码) |
| unit_number | String | 是 | 库存单位编码 |
| qty | BigDecimal | 是 | 物料明细数量 |
| warehouse_number | String | 是 | 仓库编码 |
| outinvtype_number | String | 是 | 出库库存类型编码 (固定值"110") |
| outinvstatus_number | String | 是 | 出库库存状态编码 (固定值"110") |
| outownertype | String | 是 | 出库货主类型 (固定值"bos_org") |
| outowner_number | String | 是 | 出库货主编码 (固定值"1011") |
| outkeepertype | String | 是 | 出库保管者类型 (固定值"bos_org") |
| outkeeper_number | String | 是 | 出库保管者编码 (固定值"1011") |
| project_number | String | 否 | 项目号项目编码 |
| lotnumber | String | 否 | 批号 (物料启用批号时必传) |
| gjwl_product_lotno | String | 否 | 生产批号 (对应广交云生产批号) |
| producedate | String | 否 | 生产日期 (物料启用保质期时必传) |
| expirydate | String | 否 | 到期日期 (物料启用保质期时必传) |
| invtype_number | String | 是 | 入库库存类型编码 (固定值"110") |
| invstatus_number | String | 是 | 入库库存状态编码 (固定值"110") |
| ownertype | String | 是 | 货主类型 (固定值"bos_org") |
| owner_number | String | 是 | 货主编码 (固定值"1011") |
| keepertype | String | 是 | 保管者类型 (固定值"bos_org") |
| keeper_number | String | 是 | 保管者编码 (固定值"1011") |
| price | BigDecimal | 否 | 价格 |
| entrycomment | String | 否 | 分录备注 |

## 响应格式说明

### 标准响应格式
```json  
{  
  "errorCode": "0",  
  "message": "成功",  
  "status": true,  
  "data": {}  
}  
```  

### 扩展响应格式
```json  
{  
  "errorCode": "0",  
  "message": "成功",   
"status": true,  
  "data": [  
    {  
      "success": true,  
      "message": "操作成功"  
    }  
  ]  
}  
```  

## 日志模块说明

| 接口 | 日志模块 | 描述 |  
|------|----------|------|  
| CustomerSaveRequest | kd.bd_customer.saveCustomer | 客户保存日志 |  
| CustomerSaveExtRequest | kd.bd_customer.saveCustomerExt | 客户扩展保存日志 |  
| CustomerUnAuditRequest | kd.bd_customer.unAuditCustomer | 客户反审核日志 |  
| SupplierCreateRequest | kd.bd_supplier.add | 供应商创建日志 |  
| SupplierSaveRequest | kd.bd_supplier.saveSupplier | 供应商保存日志 |  
| SupplierUpdateRequest | kd.bd_supplier.batchUpdate | 供应商批量更新日志 |  
| SupplierUnAuditRequest | kd.bd_customer.unAuditSupplier | 供应商反审核日志 |  
| PaymentRequest | kd.cas_recbill.saveRecBill | 收款单保存日志 |  
| ReceiptRecordQueryRequest | kd.bd_receipt.receiptRecordQuery | 收款流水查询日志 |  
| ReceiptClaimParamsRequest | kd.bd_receipt.receiptClaim | 收款流水认领日志 |  
| CancelClaimRequest | kd.bd_receipt.cancelClaim | 取消认领日志 |  
| PayApplyQueryRequest | kd.payApply.query | 付款申请查询日志 |  
| PayApplyAutoCreateRequest | kd.payApply.create | 付款申请创建日志 |  
| AddKdPaymentRequest | kd.cas_paybill.addSave | 金蝶付款单新增日志 |
| ClaimQueryRequest | kd.cas.billSatus.billStatusQuery | 报账单状态查询日志 |
| ProjectSaveRequest | kd.bd_project.saveProject | 项目保存日志 |
| ProjectUnAuditRequest | kd.bd_project.unAuditProject | 项目反审核日志 |
| MaterialSaveRequest | kd.bd_material.saveMaterial | 物料保存日志 |
| MaterialInventoryUpdateRequest | kd.bd_material.inventoryUpdate | 物料库存更新日志 |
| PurchaseOrderSaveRequest | kd.pm_purorderbill.savePurOrder | 采购订单保存日志 |
| SalesOrderSaveRequest | kd.sm_salorder.saveSalOrder | 销售订单保存日志 |
| SalesOutBillSaveRequest | kd.im_saloutbill.saveSaloutBill | 销售出库退货单保存日志 |
| SalPriceAdjustSaveRequest | kd.gjwl_salpriceadjust.saveSalPriceAdjust | 销售调价单保存日志 |
| SurplusBillSaveRequest | kd.im_surplusbill.saveSurpluseBill | 盘盈单保存日志 |
| DeficitBillSaveRequest | kd.im_deficitbill.saveDeficitBill | 盘亏单保存日志 |
| TransOutBillSaveRequest | kd.im_transoutbill.SaveTransOutBill | 分步调出单保存日志 |
| TransInBillSaveRequest | kd.im_transinbill.batchAdd_V2 | 分步调入单保存日志 |
| OtherOutBillSaveRequest | kd.im_otheroutbill.saveOtherOutBill | 其他出库单保存日志 |
| PurPriceAdjustSaveRequest | kd.gjwl_purpriceadjust.savePurPriceAdjust | 采购调价单保存日志 |
| PurTaxAdjustSaveRequest | kd.gjwl_purtaxadjust.savePurTaxAdjust | 采购调税单保存日志 |
| PurInBillSaveRequest | kd.im_purinbill.savePruinBill | 采购入库退料单保存日志 |
| RebateSaveRequest | kd.gjwl_rebate.saveRebate | 返利单保存日志 |
| CreditStatusQueryRequest | kd.ccm.credit.getCreditBalance | 查询信用状况日志 |

## 特殊说明

### 标准格式包装
- 大部分接口使用标准格式包装 (standard() = true)
- 以下接口不使用标准格式包装 (standard() = false):
    - ReceiptRecordQueryRequest
    - ReceiptClaimParamsRequest
    - CancelClaimRequest
    - ClaimQueryRequest
    - CreditStatusQueryRequest

### 下推并保存接口参数

#### PushAndSaveRequestParam (下推并保存请求参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| sourceEntityNumber | String | 是 | 源单单据标识 |
| targetEntityNumber | String | 是 | 目标单单据标识 |
| ruleId | String | 是 | 转换规则ID |
| sourceBills | List&lt;SelectedBill&gt; | 是 | 源单单据信息 |

#### SelectedBill (源单单据信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| billId | String | 是 | 源单ID |
| entryIds | List&lt;String&gt; | 否 | 源单分录ID集合，为空则整单下推 |

#### PushApiResult (下推结果)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| result | List&lt;Map&gt; | 否 | 返回结果详细信息 |
| pushResult | List&lt;PushResult&gt; | 否 | 下推并保存返回结果详细信息 |
| targetResult | List&lt;TargetResult&gt; | 否 | 下推并保存返回目标单据详细信息 |

### 信用管理接口参数

#### CreditStatusQueryParam (查询信用状况请求参数)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| currencynumber | String | 否 | 币别编码 (默认"CNY") |
| orgscope | String | 否 | 控制组织范围 (固定值"SINGLE") |
| orgnumberset | Set&lt;String&gt; | 否 | 授信组织编码集合 (默认["1011"]) |
| dimensionnumber | String | 是 | 信控维度编码 (固定值"CUSTOMER") |
| roletype0 | String | 否 | 维度成员类型0 (固定值"bd_customer") |
| rolenumberset0 | Set&lt;String&gt; | 否 | 维度成员0编码集合 (客户编码集合) |
| schemenumber | String | 否 | 信用控制方案编码 (固定值"XKFA-SYS-001") |

#### CreditStatusQueryResponseData (查询信用状况响应数据)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| archiveidSet | Set&lt;Long&gt; | 否 | 信用档案ID |
| orgscope | String | 否 | 控制组织范围 |
| archiveorgnumberlist | List&lt;String&gt; | 否 | 授信组织 |
| archiveobjectmap | HashMap&lt;String, String&gt; | 否 | 信用对象 |
| schemenumber | String | 否 | 信用控制方案 |
| dimensionnumber | String | 否 | 信控维度 |
| currencynumber | String | 否 | 币别 |
| singlecurcontrol | Boolean | 否 | 币别隔离 |
| quotaamount | BigDecimal | 否 | 信用额度 |
| tempamount | BigDecimal | 否 | 临时信用额度 |
| occupyamount | BigDecimal | 否 | 实际占用额度 |
| balance | BigDecimal | 否 | 可用额度 |
| quotaoverdays | BigDecimal | 否 | 信用天数 |
| tempoverdays | BigDecimal | 否 | 临时信用天数 |
| actualoverdays | BigDecimal | 否 | 逾期天数 |
| overdaysbal | BigDecimal | 否 | 超标逾期天数 |
| quotaoveramount | BigDecimal | 否 | 允许逾期额度 |
| tempoveramount | BigDecimal | 否 | 临时逾期额度 |
| actualoveramount | BigDecimal | 否 | 实际逾期额度 |
| overamountbal | BigDecimal | 否 | 超标逾期金额 |
| exceed | Boolean | 否 | 是否超标 |
| grade | String | 否 | 信用等级 |
| quoarchivemap | HashMap&lt;String, Long&gt; | 否 | 额度类型档案ID |
| exratetablenumber | String | 否 | 汇率表 |

### 返利单管理接口参数

#### Rebate (返利单信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 否 | 单据ID (修改时需传) |
| billno | String | 是 | 确认单号 |
| org_number | String | 是 | 组织编码 (固定值"1011") |
| gjwl_billtype_number | String | 是 | 单据类型编码 (固定值"5FyQB") |
| gjwl_contactunittype | String | 是 | 往来单位类型 (固定值"bd_supplier") |
| gjwl_contactunit_number | String | 是 | 往来单位编码 |
| gjwl_rebateamountsum | BigDecimal | 否 | 应返利金额 |
| gjwl_confirmamountsum | BigDecimal | 否 | 确认返利金额 |
| gjwl_outsidecratedate | String | 否 | 外部生成时间 |
| gjwl_outsideauditdate | String | 否 | 外部审核时间 |
| gjwl_remark | String | 否 | 备注 |
| entryentity | List<RebateEntry> | 否 | 返利明细列表 |

#### RebateEntry (返利单明细信息)
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 明细ID (修改时需传) |
| gjwl_rebateamount | BigDecimal | 否 | 应返利金额 |
| gjwl_confirmamount | BigDecimal | 否 | 确认返利金额 |
| gjwl_bizdate | String | 否 | 业务日期 |
| gjwl_mainbillno | String | 否 | 核心单号 |
| gjwl_bizno | String | 否 | 业务单据 |
| gjwl_biztype | String | 否 | 单据类型 |
| gjwl_protocolno | String | 否 | 协议编号 |
| gjwl_protocolname | String | 否 | 协议名称 |
| gjwl_clauseno | String | 否 | 条款编号 |
| gjwl_materiel_number | String | 是 | 物料编码 |
| gjwl_producer | String | 否 | 生产厂家/受托生产企业 |
| gjwl_qty | BigDecimal | 否 | 数量 |
| gjwl_price | BigDecimal | 否 | 单价 |
| gjwl_amount | BigDecimal | 否 | 金额 |
| gjwl_lotno | String | 否 | 批号 |
| gjwl_product_lotno | String | 否 | 生产批号/序列号 |
| gjwl_rebatepoint | BigDecimal | 否 | 返利点数 |
| gjwl_allocateqty | BigDecimal | 否 | 分摊数量 |
| gjwl_protocoltype | String | 否 | 协议分类 |
| gjwl_rebatetype | String | 否 | 返利类型 |
| gjwl_ledgerid | String | 否 | 台账ID |
| gjwl_rebatesource | String | 否 | 返利来源 |
| gjwl_rebatepaytype | String | 否 | 返利支付方式 |

### 请求头要求
- Content-Type: application/json
- accesstoken: 访问令牌
- x-acgw-identity: 身份标识
- Idempotency-Key: 幂等性键值(UUID)

### 超时配置
- 连接超时: 60秒
- 读取超时: 60秒
- 写入超时: 60秒