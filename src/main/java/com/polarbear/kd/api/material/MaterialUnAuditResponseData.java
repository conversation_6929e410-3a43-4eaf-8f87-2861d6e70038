package com.polarbear.kd.api.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 物料反审核响应数据
 * 
 * <AUTHOR>
 * @date 2025-09-29
 */
@Data
public class MaterialUnAuditResponseData {

    /** 操作执行的过滤条件 */
    @JsonProperty("filter")
    private String filter;

    /** 返回结果详细信息 */
    @JsonProperty("result")
    private List<UnAuditResultItem> result;

    /** 操作失败数量 */
    @JsonProperty("failCount")
    private String failCount;

    /** 操作成功数量 */
    @JsonProperty("successCount")
    private String successCount;

    /** 总数 */
    @JsonProperty("totalCount")
    private String totalCount;

    /** 反审核结果项 */
    @Data
    public static class UnAuditResultItem {

        /** 操作是否成功 */
        @JsonProperty("billStatus")
        private Boolean billStatus;

        /** 错误信息数组 */
        @JsonProperty("errors")
        private List<Object> errors;

        /** 物料ID */
        @JsonProperty("id")
        private String id;

        /** 物料编码 */
        @JsonProperty("number")
        private String number;
    }
}
