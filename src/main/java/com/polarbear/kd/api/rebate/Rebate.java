package com.polarbear.kd.api.rebate;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 返利单实体类
 * 
 * <AUTHOR>
 * @apiNote 广交云.【采购返利单】.审核后 推送 金蝶云·星空旗舰版.【返利单】
 *          广交云.【销售折让单】.审核后 推送 金蝶云·星空旗舰版.【返利单】
 * @date 2025-09-28
 */
@Data
public class Rebate {
    
    /**
     * id (修改时需传)
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 确认单号
     */
    @JsonProperty("billno")
    private String billno;
    
    /**
     * 组织.编码 (固定值，传"1011")
     */
    @JsonProperty("org_number")
    private String orgNumber;
    
    /**
     * 单据类型.编码 (固定值，传"5FyQB")
     */
    @JsonProperty("gjwl_billtype_number")
    private String gjwlBilltypeNumber;
    
    /**
     * 往来单位类型 (固定值，传"bd_supplier")
     */
    @JsonProperty("gjwl_contactunittype")
    private String gjwlContactunittype;
    
    /**
     * 往来单位.编码
     */
    @JsonProperty("gjwl_contactunit_number")
    private String gjwlContactunitNumber;
    
    /**
     * 应返利金额
     */
    @JsonProperty("gjwl_rebateamountsum")
    private BigDecimal gjwlRebateamountsum;
    
    /**
     * 确认返利金额
     */
    @JsonProperty("gjwl_confirmamountsum")
    private BigDecimal gjwlConfirmamountsum;
    
    /**
     * 外部生成时间
     */
    @JsonProperty("gjwl_outsidecratedate")
    private String gjwlOutsidecratedate;
    
    /**
     * 外部审核时间
     */
    @JsonProperty("gjwl_outsideauditdate")
    private String gjwlOutsideauditdate;
    
    /**
     * 备注
     */
    @JsonProperty("gjwl_remark")
    private String gjwlRemark;
    
    /**
     * 返利明细
     */
    @JsonProperty("entryentity")
    private List<RebateEntry> entryentity;
    
    // 常量定义
    
    /**
     * 组织编码默认值
     */
    public static final String ORG_DEFAULT = "1011";
    
    /**
     * 单据类型编码默认值
     */
    public static final String BILLTYPE_DEFAULT = "5FyQB";
    
    /**
     * 往来单位类型默认值
     */
    public static final String CONTACT_UNIT_TYPE_SUPPLIER = "bd_supplier";
}
