## 物料反审核接口

### 接口描述：

- 广交云.【物料】.审核后需修改保存前需 **反审核** 金蝶云·星空旗舰版.【物料】

**请求 URL**：

{{http/https}}://{{localhost}}/kapi/v2/gjwl/basedata/bd_material/unAuditMaterial

**请求 URL（沙箱环境）:**

https://gjwl.test.kdgalaxy.com/kapi/v2/gjwl/basedata/bd_material/unAuditMaterial

**调用方式**：HTTP调用

**请求方式**：POST

**请求类型**：Content-Type:application/json

**请求Header参数：(和 [3.1.客户保存提交接口](#_客户保存提交接口) 请求Header参数的一致)**

**请求Body参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **是否必填** | **参数类型** | **描述说明** |
| numbers | "Cus-000001","Cus-000002" | 是   | Array&lt;String&gt; | 客户编码 |

**请求参数示例（沙箱环境）:**

{

"data":{

"numbers":\[

"test-0018"

\]

}

}

**返回参数：**

|     |     |     |     |     |
| --- | --- | --- | --- | --- |
| **参数名** | **参数值** | **参数类型** | **层级** | **描述说明** |
| data | {}  | Object | 1   | 结果数据 |
| filter | 操作执行的过滤条件，如：billno in ('CGSQ-220228-000258','CGSQ-220228-000259') | String | 2   | 过滤条件 |
| result | "\[\]" | Array&lt;Map&gt; | 2   | 返回结果详细信息 |
| failCount | "2" | String | 2   | 操作失败数量 |
| successCount | "1" | String | 2   | 操作成功数量 |
| totalCount | "3" | String | 2   | 总数  |
| errorCode | 成功时为0，失败时会返回错误码如400 | String | 1   | 错误码 |
| message | 成功时为空，失败时会返回错误信息如“操作失败” | String | 1   | 接口调用错误信息 |
| status | true/false | Boolean | 1   | 接口访问是否成功 |

**返回参数示例:**

{

"data":{

"failCount":"0",

"filter":"\[number IN ('test-0018')\]",

"result":\[

{

"billStatus":true,

"errors":\[\],

"id":"2058208592103736320",

"number":"Sup-000001"

}

\],

"successCount":"1",

"totalCount":"1"

},

"errorCode":"0",

"message":null,

"status":true

}
